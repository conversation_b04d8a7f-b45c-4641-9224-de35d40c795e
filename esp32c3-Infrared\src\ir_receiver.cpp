#include "ir_receiver.h"

// 构造函数
IRReceiver::IRReceiver(const IRConfig& cfg) : config(cfg) {
    irrecv = new IRrecv(config.recvPin, config.bufferSize, config.timeout, config.enablePullup);
}

// 析构函数
IRReceiver::~IRReceiver() {
    if (irrecv) {
        delete irrecv;
        irrecv = nullptr;
    }
}

// 初始化接收器
void IRReceiver::begin() {
    if (irrecv) {
        irrecv->enableIRIn();
        Serial.println("红外接收器已启动");
        Serial.print("接收引脚: GPIO");
        Serial.println(config.recvPin);
        Serial.print("缓冲区大小: ");
        Serial.println(config.bufferSize);
        Serial.print("超时时间: ");
        Serial.print(config.timeout);
        Serial.println("ms");
    }
}

// 检查是否有新的红外信号
bool IRReceiver::available() {
    if (irrecv) {
        return irrecv->decode(&results);
    }
    return false;
}

// 获取解码结果
decode_results IRReceiver::getResults() {
    return results;
}

// 恢复接收器以接收下一个信号
void IRReceiver::resume() {
    if (irrecv) {
        irrecv->resume();
    }
}

// 打印信号详细信息
void IRReceiver::printSignalInfo() {
    Serial.println("接收到红外信号:");
    Serial.print("协议: ");
    Serial.println(getProtocolName());
    
    Serial.print("数据: 0x");
    serialPrintUint64(results.value, HEX);
    Serial.println();
    
    Serial.print("位数: ");
    Serial.println(results.bits);
    
    Serial.print("原始数据 (");
    Serial.print(results.rawlen);
    Serial.print("): ");
    
    // 打印原始时序数据（限制输出长度）
    uint16_t maxPrint = min(results.rawlen, (uint16_t)20);
    for (uint16_t i = 1; i < maxPrint; i++) {
        Serial.print(results.rawbuf[i] * kRawTick);
        if (i < maxPrint - 1) {
            Serial.print(", ");
        }
    }
    if (results.rawlen > 20) {
        Serial.print("...");
    }
    Serial.println();
    
    if (isRepeat()) {
        Serial.println("(重复信号)");
    }
    
    Serial.println("----------------------------------------");
}

// 获取协议名称
String IRReceiver::getProtocolName() {
    return String(typeToString(results.decode_type));
}

// 获取信号数据
uint64_t IRReceiver::getSignalData() {
    return results.value;
}

// 获取信号位数
uint16_t IRReceiver::getSignalBits() {
    return results.bits;
}

// 是否为重复信号
bool IRReceiver::isRepeat() {
    return results.repeat;
}

// 停用接收器
void IRReceiver::end() {
    if (irrecv) {
        irrecv->disableIRIn();
        Serial.println("红外接收器已停用");
    }
}
