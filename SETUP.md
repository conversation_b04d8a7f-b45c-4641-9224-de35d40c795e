# 配置说明

## WiFi 配置

本项目使用 `include/secrets.h` 文件来管理WiFi配置，这样可以避免在主代码中暴露敏感信息。

### 修改WiFi参数

打开 `include/secrets.h` 文件，修改以下参数：

```cpp
// WiFi Station Mode (connect to existing WiFi)
const char* WIFI_SSID = "YOUR_WIFI_NAME";        // 修改为您的WiFi名称
const char* WIFI_PASSWORD = "YOUR_WIFI_PASSWORD"; // 修改为您的WiFi密码

// WiFi Access Point Mode (create own WiFi network for LD2450 radar)
const char* RADAR_AP_SSID = "ESP32-LD2450";      // 热点名称（可自定义）
const char* RADAR_AP_PASSWORD = "12345678";       // 热点密码（可自定义）
const bool USE_AP_MODE = false;                   // 设置为true使用热点模式

// Web Server settings for radar visualization
const int WEB_SERVER_PORT = 80;                   // Web服务器端口
const char* MDNS_HOST_NAME = "esp32-ld2450";     // mDNS主机名
```

### 工作模式选择

1. **Station模式（默认）**：连接到现有WiFi网络
   - 设置 `USE_AP_MODE = false`
   - 配置 `WIFI_SSID` 和 `WIFI_PASSWORD`

2. **AP模式**：创建自己的WiFi热点
   - 设置 `USE_AP_MODE = true`
   - 配置 `RADAR_AP_SSID` 和 `RADAR_AP_PASSWORD`

### 示例配置

```cpp
// 连接到家庭WiFi
const char* WIFI_SSID = "My_Home_WiFi";
const char* WIFI_PASSWORD = "mypassword123";
const bool USE_AP_MODE = false;
```

或者

```cpp
// 创建独立热点
const char* RADAR_AP_SSID = "LD2450_Radar";
const char* RADAR_AP_PASSWORD = "radar2024";
const bool USE_AP_MODE = true;
```

## 部署步骤

1. **修改WiFi配置**（见上方说明）

2. **编译并上传文件系统**
   ```bash
   pio run --target uploadfs
   ```

3. **编译并上传程序**
   ```bash
   pio run --target upload
   ```

4. **查看串口输出**
   - 打开串口监视器（波特率：115200）
   - 查看WiFi连接状态和IP地址

5. **访问Web界面**
   - 如果WiFi连接成功，访问显示的IP地址
   - 如果WiFi连接失败，设备会自动创建热点：
     - 热点名称：ESP32-LD2450
     - 密码：12345678
     - IP地址：***********

## Web界面功能

- 🎯 **实时雷达视图**：图形化显示目标位置
- 📊 **目标列表**：显示所有检测到的目标详细信息
- 📈 **统计数据**：检测次数、最大距离、活跃目标数
- 🎮 **控制面板**：开始/停止检测、清除历史、导出数据
- 📝 **系统日志**：实时显示系统运行状态

## 数据存储

- 所有目标数据自动保存到ESP32的LittleFS文件系统
- 可通过Web界面导出所有历史数据
- 数据格式为JSON，便于后续分析

## 故障排除

### WiFi连接问题
- 确认WiFi名称和密码正确
- 检查WiFi信号强度
- 如果仍无法连接，使用热点模式

### 传感器数据问题
- 检查LD2450电源连接（3.3V）
- 确认串口连接正确（TX->GPIO1, RX->GPIO0）
- 查看串口监视器的调试信息

### Web界面访问问题
- 确认设备已连接到网络
- 尝试关闭防火墙
- 使用不同浏览器测试
