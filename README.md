# ESP32-C3 LD2450 雷达传感器Web监控系统

本项目使用ESP32-C3微控制器与LD2450雷达传感器构建了一个完整的Web监控系统，支持实时多目标检测、可视化显示和数据导出功能。

## 项目概述

LD2450是一款高精度的24GHz雷达传感器，能够同时检测多个目标的位置信息。本项目通过ESP32-C3读取传感器数据，提供Web界面进行实时监控和数据可视化。

## 主要特性

### 🎯 雷达功能
- ✅ 实时多目标检测（最多3个目标同时）
- ✅ 高精度坐标定位（X、Y坐标，毫米级）
- ✅ 速度检测（厘米/秒）
- ✅ 距离和角度计算
- ✅ 智能帧同步和数据验证

### 🌐 Web服务器
- ✅ 现代化响应式Web界面
- ✅ 实时雷达视图可视化
- ✅ RESTful API接口
- ✅ 数据导出功能
- ✅ 远程控制检测开关

### 📡 网络功能
- ✅ WiFi连接和AP热点模式
- ✅ mDNS域名解析支持
- ✅ 实时数据推送
- ✅ 多设备同时访问

## 硬件要求

### 主要组件
- ESP32-C3开发板
- LD2450雷达传感器模块
- 杜邦线若干

### 技术规格
- **ESP32-C3**: RISC-V单核处理器，WiFi支持，160MHz
- **LD2450**: 24GHz雷达，检测距离0.2-6米，支持最多3个目标同时检测

## 硬件连接

```
LD2450模块    ESP32-C3
├─ VCC   →   5V
├─ GND   →   GND  
├─ TX    →   GPIO1 (RX)
└─ RX    →   GPIO0 (TX)
```

### 重要说明
- LD2450模块工作电压为3.3V
- 串口通信参数：256000波特率，8数据位，无奇偶校验，1停止位
- 使用ESP32-C3的UART1进行数据通信

## 软件环境

### 开发平台
- **PlatformIO** (推荐) 或 Arduino IDE
- **框架**: Arduino Framework
- **平台**: Espressif ESP32

### 依赖库
```ini
lib_deps = 
    ESPAsyncWebServer
    ArduinoJson
    LittleFS
    ESPmDNS
```

## 项目结构

```
esp32c3-ld2450/
├── src/
│   └── main.cpp              # 主程序文件
├── include/
│   └── secrets.h             # WiFi配置文件
├── data/
│   └── index.html            # Web前端界面
├── lib/                      # 自定义库目录
├── test/                     # 测试文件目录
├── platformio.ini            # PlatformIO配置文件
├── DataAnalysisDemo.kt       # Kotlin参考实现
├── SETUP.md                  # 详细安装说明
└── README.md                 # 项目说明文档
```
## 快速开始

### 1. 配置WiFi

创建 `include/secrets.h` 文件：

```cpp
#ifndef SECRETS_H
#define SECRETS_H

// WiFi配置
#define WIFI_SSID "你的WiFi名称"
#define WIFI_PASSWORD "你的WiFi密码"

// Web服务器配置
#define WEB_SERVER_PORT 80
#define MDNS_HOST_NAME "esp32-radar"

// AP模式配置
#define USE_AP_MODE false
#define RADAR_AP_SSID "ESP32-Radar"
#define RADAR_AP_PASSWORD "12345678"

#endif
```

### 2. 编译和上传

```bash
# 进入项目目录
cd esp32c3-ld2450

# 编译项目
pio run

# 上传固件
pio run --target upload

# 上传文件系统（Web界面）
pio run --target uploadfs

# 打开串口监视器
pio device monitor --baud 115200
```

### 3. 访问Web界面

上传完成后，在串口监视器中查看设备IP地址，然后在浏览器中访问：

- **通过IP**: `http://192.168.x.x`
- **通过mDNS**: `http://esp32-radar.local`

## Web界面功能

### 🎯 实时雷达视图

- 圆形雷达显示界面
- 实时目标位置可视化
- 动态更新检测结果
- 目标轨迹显示

### 📊 数据监控

- 实时目标列表
- 坐标、距离、角度信息
- 检测状态显示
- 系统运行统计

### 🎛️ 控制面板

- 开始/停止检测
- 清除历史数据
- 数据导出功能
- 系统状态监控

## API接口

### 获取实时目标数据

```http
GET /api/targets
```

**响应格式:**

```json
{
  "success": true,
  "timestamp": 123456,
  "lastDetectionTime": 123450,
  "targets": [
    {
      "x": 150.5,
      "y": 200.0,
      "speed": 12.3,
      "resolution": 1.0,
      "distance": 0.25,
      "angle": 26.6,
      "timestamp": 123450
    }
  ]
}
```

### 检测控制

```http
POST /api/detection/start    # 开始检测
POST /api/detection/stop     # 停止检测
GET  /api/detection/status   # 获取状态
```

### 系统状态

```http
GET /api/realtime/status
```

**响应格式:**

```json
{
  "success": true,
  "uptime": 123456,
  "isDetecting": true,
  "currentTargetCount": 1,
  "serialAvailable": 0,
  "freeHeap": 245760,
  "latestTarget": {
    "x": 150.5,
    "y": 200.0,
    "distance": 0.25,
    "angle": 26.6
  }
}
```

## 技术细节

### 数据处理流程

1. **帧同步**: 检测LD2450数据帧头 `AAFF0300`
2. **数据读取**: 读取完整30字节数据帧
3. **坐标解析**: 解析X、Y坐标（小端字节序）
4. **计算处理**: 计算距离、角度等衍生数据
5. **实时推送**: 通过API提供给Web前端

### 坐标系说明

- **X轴**: 水平方向，正值向右，负值向左
- **Y轴**: 垂直方向，正值向前，负值向后  
- **角度**: 以Y轴正方向为0度，右侧为正角度，左侧为负角度
- **距离**: 毫米精度，API输出转换为米

### 性能优化

- 智能帧缓冲和同步机制
- 最小化串口阻塞时间
- 任务调度优化（yield()调用）
- 禁用看门狗避免Web服务器冲突

## 故障排除

### 常见问题

#### 1. 无法访问Web界面

**检查项目:**

- 确认WiFi连接状态
- 检查IP地址是否正确
- 尝试使用mDNS域名访问
- 检查防火墙设置

#### 2. 雷达数据异常

**可能原因:**

- 硬件连接问题
- 电源供电不稳定
- 串口波特率错误

**解决方案:**

- 检查接线是否牢固
- 确认5V供电
- 验证串口配置

#### 3. Web界面无法更新数据

**检查项目:**

- 确认检测功能已启用
- 检查API响应是否正常
- 查看浏览器控制台错误
- 尝试刷新页面

### 调试输出

程序提供详细的调试信息：

```
=== ESP32-C3 LD2450 Radar Sensor Started ===
WiFi connected! IP address: *************
mDNS responder started: esp32-radar.local
Web server started on port: 80
==========================================

=== Heartbeat - Detection: ON, Available data: 30 bytes ===
=== Processing serial data ===
Valid frame length, parsing...
=== Parsing completed, found targets: 1 ===
SUCCESS: Found 1 targets and saved to memory
Target 1 - X:150.5mm Y:200.0mm Speed:12.3cm/s Res:1.0mm
=== Processing completed ===
```

## 扩展功能

### 可选配置

- 修改检测灵敏度
- 自定义Web界面样式
- 添加数据记录功能
- 集成其他传感器

### 二次开发

项目基于Arduino框架，易于扩展：

- 添加MQTT支持
- 集成云端服务
- 增加语音提示
- 手机APP开发

## 版本历史

- **v2.0**: Web监控系统
  - 完整Web界面
  - RESTful API
  - mDNS支持
  - 实时数据可视化

- **v1.0**: 基础功能实现
  - 多目标检测
  - 串口通信
  - 数据解析和输出

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 相关链接

- [ESP32-C3官方文档](https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/)
- [PlatformIO官网](https://platformio.org/)
- [LD2450数据手册](链接待补充)
- [项目详细安装说明](SETUP.md)

---

**⚠️ 注意**: 请确保在使用前仔细检查硬件连接，避免因接线错误导致设备损坏。

**🚀 快速体验**: 上传代码后，直接在浏览器访问设备IP地址即可开始使用！
