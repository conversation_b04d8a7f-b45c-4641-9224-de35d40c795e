/*
 * ESP32-C3 红外接收器自定义配置示例
 * 
 * 此示例展示如何使用自定义配置创建红外接收器
 */

#include <Arduino.h>
#include "ir_receiver.h"

// 自定义红外接收器配置
IRConfig customConfig = {
    .recvPin = 3,           // 使用GPIO3作为接收引脚
    .bufferSize = 2048,     // 使用2KB缓冲区
    .timeout = 20,          // 20ms超时
    .enablePullup = true    // 启用内部上拉电阻
};

// 创建使用自定义配置的红外接收器
IRReceiver customIrReceiver(customConfig);

void setup() {
    Serial.begin(115200);
    while (!Serial) {
        delay(50);
    }
    
    Serial.println("ESP32-C3 红外接收器 - 自定义配置示例");
    Serial.println("=====================================");
    
    // 初始化红外接收器
    customIrReceiver.begin();
    Serial.println("等待红外信号...");
}

void loop() {
    if (customIrReceiver.available()) {
        // 获取信号信息
        String protocol = customIrReceiver.getProtocolName();
        uint64_t data = customIrReceiver.getSignalData();
        uint16_t bits = customIrReceiver.getSignalBits();
        bool repeat = customIrReceiver.isRepeat();
        
        // 简化的信号显示
        Serial.print("协议: ");
        Serial.print(protocol);
        Serial.print(", 数据: 0x");
        Serial.print((uint32_t)data, HEX);
        Serial.print(", 位数: ");
        Serial.print(bits);
        if (repeat) {
            Serial.print(" (重复)");
        }
        Serial.println();
        
        // 根据不同协议执行不同操作的示例
        if (protocol == "NEC") {
            handleNECProtocol(data);
        } else if (protocol == "SONY") {
            handleSONYProtocol(data);
        } else if (protocol == "RC5") {
            handleRC5Protocol(data);
        } else {
            Serial.println("未知协议，使用通用处理");
        }
        
        customIrReceiver.resume();
    }
    
    delay(50);
}

// NEC协议处理函数
void handleNECProtocol(uint64_t data) {
    Serial.println("处理NEC协议信号");
    
    // NEC协议的数据格式：地址(8位) + ~地址(8位) + 命令(8位) + ~命令(8位)
    uint8_t address = (data >> 24) & 0xFF;
    uint8_t command = (data >> 8) & 0xFF;
    
    Serial.print("地址: 0x");
    Serial.print(address, HEX);
    Serial.print(", 命令: 0x");
    Serial.println(command, HEX);
    
    // 在这里添加具体的命令处理逻辑
    switch (command) {
        case 0x45: // 示例：电源键
            Serial.println("电源键被按下");
            break;
        case 0x46: // 示例：音量+
            Serial.println("音量+键被按下");
            break;
        case 0x47: // 示例：音量-
            Serial.println("音量-键被按下");
            break;
        default:
            Serial.print("未定义的命令: 0x");
            Serial.println(command, HEX);
            break;
    }
}

// SONY协议处理函数
void handleSONYProtocol(uint64_t data) {
    Serial.println("处理SONY协议信号");
    // 添加SONY协议特定的处理逻辑
}

// RC5协议处理函数
void handleRC5Protocol(uint64_t data) {
    Serial.println("处理RC5协议信号");
    // 添加RC5协议特定的处理逻辑
}
