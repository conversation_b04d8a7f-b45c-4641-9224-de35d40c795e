# ESP32-C3 红外接收器接线图

## 硬件需求

### ESP32-C3开发板
- 任何ESP32-C3开发板（如：ESP32-C3-DevKitM-1）

### 红外接收器模块
推荐使用以下任一型号：
- **VS1838B** - 最常见，价格便宜
- **TSOP4838** - 抗干扰能力强
- **IRM-3638T** - 接收距离远

## 接线连接

### 标准连接（使用GPIO2）

```
ESP32-C3 开发板          红外接收器模块
┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │
│            3.3V │──────│ VCC             │
│                 │      │                 │
│             GND │──────│ GND             │
│                 │      │                 │
│           GPIO2 │──────│ OUT (Signal)    │
│                 │      │                 │
└─────────────────┘      └─────────────────┘
```

### 引脚说明

| ESP32-C3引脚 | 红外接收器引脚 | 说明 |
|-------------|---------------|------|
| 3.3V        | VCC           | 电源正极 |
| GND         | GND           | 电源负极 |
| GPIO2       | OUT/Signal    | 数据信号输出 |

### 可选连接（使用其他GPIO）

如果GPIO2被占用，可以使用其他GPIO引脚：

**可用的GPIO引脚：**
- GPIO0, GPIO1, GPIO2, GPIO3, GPIO4, GPIO5
- GPIO6, GPIO7, GPIO8, GPIO9, GPIO10
- GPIO18, GPIO19, GPIO20, GPIO21

**注意：** 避免使用以下引脚：
- GPIO11, GPIO12, GPIO13 (SPI Flash)
- GPIO14, GPIO15, GPIO16, GPIO17 (PSRAM，如果使用)

### 自定义引脚配置示例

```cpp
// 使用GPIO3作为接收引脚
IRConfig customConfig = {
    .recvPin = 3,           // 改为GPIO3
    .bufferSize = 1024,
    .timeout = 15,
    .enablePullup = true
};

IRReceiver irReceiver(customConfig);
```

## 物理连接建议

### 1. 使用面包板连接
```
面包板布局示例：

    ESP32-C3                红外接收器
    ┌─────┐                ┌─────┐
    │ 3V3 │────────────────│ VCC │
    │ GND │────────────────│ GND │
    │ GP2 │────────────────│ OUT │
    └─────┘                └─────┘
```

### 2. 使用杜邦线直连
- 红色线：3.3V → VCC
- 黑色线：GND → GND  
- 黄色线：GPIO2 → OUT

### 3. PCB设计建议
如果制作PCB，建议：
- 在红外接收器附近添加100nF去耦电容
- 保持信号线尽可能短
- 远离高频信号源

## 故障排除

### 连接检查清单

1. **电源连接**
   - [ ] 3.3V正确连接到VCC
   - [ ] GND正确连接到GND
   - [ ] 电压测量：VCC应为3.3V

2. **信号连接**
   - [ ] GPIO2正确连接到OUT
   - [ ] 连接牢固，无松动
   - [ ] 无短路现象

3. **模块检查**
   - [ ] 红外接收器模块完好
   - [ ] 方向正确（通常有标识）
   - [ ] 型号兼容（38kHz载波频率）

### 常见问题

**问题1：无法接收信号**
- 检查电源连接
- 确认引脚配置正确
- 测试红外遥控器是否工作

**问题2：接收距离太近**
- 检查电源电压是否稳定
- 尝试更换红外接收器模块
- 确认遥控器电池电量充足

**问题3：误触发或干扰**
- 远离荧光灯等干扰源
- 添加去耦电容
- 调整超时参数

## 测试方法

### 1. 硬件测试
使用万用表检查：
- VCC与GND之间电压：应为3.3V
- OUT引脚在无信号时：应为高电平（约3.3V）
- OUT引脚在接收信号时：应有波动

### 2. 软件测试
运行测试程序：
```cpp
// 上传test/test_ir_receiver.cpp到ESP32-C3
// 打开串口监视器，波特率115200
// 使用遥控器测试接收功能
```

### 3. 信号质量测试
- 测试不同距离的接收效果
- 测试不同角度的接收效果
- 测试不同遥控器的兼容性
