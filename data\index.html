<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LD2450 雷达目标可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .subtitle {
            color: #666;
            font-size: 1.2em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .radar-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        
        .radar-canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
            background: radial-gradient(circle, #2c3e50, #34495e);
            position: relative;
            overflow: hidden;
        }
        
        .info-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .target-list {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .target-item {
            padding: 10px;
            margin: 5px 0;
            background: #f1f3f4;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }
        
        .target-coords {
            font-weight: bold;
            color: #333;
        }
        
        .target-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .controls {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .log-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            grid-column: 1 / -1;
        }
        
        .log-content {
            background: #2d3436;
            color: #00b894;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        
        .timestamp {
            color: #74b9ff;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 LD2450 雷达目标检测</h1>
            <p class="subtitle">实时目标位置可视化系统</p>
        </div>
        
        <div class="main-content">
            <div class="radar-container">
                <h3 style="margin-bottom: 15px; color: #333;">雷达视图</h3>
                <canvas id="radarCanvas" class="radar-canvas" width="600" height="500"></canvas>
            </div>
            
            <div class="info-panel">
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot status-offline"></div>
                    <span id="statusText">连接状态: 离线</span>
                </div>
                
                <div class="controls">
                    <h4 style="margin-bottom: 10px;">控制面板</h4>
                    <button class="btn" onclick="startDetection()">开始检测</button>
                    <button class="btn" onclick="stopDetection()">停止检测</button>
                    <button class="btn" onclick="clearHistory()">清除历史</button>
                    <button class="btn" onclick="downloadData()">导出数据</button>
                </div>
                
                <div class="target-list">
                    <h4 style="margin-bottom: 10px;">当前目标</h4>
                    <div id="targetList">
                        <div style="text-align: center; color: #888; padding: 20px;">
                            暂无检测到目标
                        </div>
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 15px;">
                    <h4 style="margin-bottom: 10px;">统计信息</h4>
                    <div>检测次数: <span id="detectionCount">0</span></div>
                    <div>最大距离: <span id="maxDistance">0</span>m</div>
                    <div>活跃目标: <span id="activeTargets">0</span></div>
                </div>
            </div>
        </div>
        
        <div class="log-panel">
            <h3 style="margin-bottom: 15px; color: #333;">系统日志</h3>
            <div id="logContent" class="log-content"></div>
        </div>
    </div>    <script>
        let isDetecting = true;
        let detectionCount = 0;
        let maxDistance = 0;
        let targets = [];
        let canvas, ctx;
        let dataInterval = null; // 用于控制数据获取定时器
        
        // 初始化画布
        function initCanvas() {
            canvas = document.getElementById('radarCanvas');
            ctx = canvas.getContext('2d');
            
            // 设置画布实际大小
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
            
            drawRadarGrid();
        }
        
        // 绘制雷达网格
        function drawRadarGrid() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const maxRadius = Math.min(centerX, centerY) - 20;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景渐变
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, maxRadius);
            gradient.addColorStop(0, 'rgba(46, 204, 113, 0.1)');
            gradient.addColorStop(1, 'rgba(52, 73, 94, 0.8)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制同心圆
            ctx.strokeStyle = 'rgba(74, 185, 255, 0.5)';
            ctx.lineWidth = 1;
            for (let i = 1; i <= 4; i++) {
                ctx.beginPath();
                ctx.arc(centerX, centerY, (maxRadius / 4) * i, 0, 2 * Math.PI);
                ctx.stroke();
            }
            
            // 绘制十字线
            ctx.beginPath();
            ctx.moveTo(centerX, 20);
            ctx.lineTo(centerX, canvas.height - 20);
            ctx.moveTo(20, centerY);
            ctx.lineTo(canvas.width - 20, centerY);
            ctx.stroke();
            
            // 绘制角度线
            for (let angle = 0; angle < 360; angle += 30) {
                const radians = (angle * Math.PI) / 180;
                const x = centerX + Math.cos(radians) * maxRadius;
                const y = centerY + Math.sin(radians) * maxRadius;
                
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.strokeStyle = 'rgba(74, 185, 255, 0.3)';
                ctx.stroke();
            }
            
            // 绘制距离标签
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            for (let i = 1; i <= 4; i++) {
                const radius = (maxRadius / 4) * i;
                const distance = (5 / 4) * i; // 假设最大检测距离为5米
                ctx.fillText(`${distance}m`, centerX + radius - 15, centerY - 5);
            }
        }
        
        // 绘制目标
        function drawTargets() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const maxRadius = Math.min(centerX, centerY) - 20;
            const scale = maxRadius / 5000; // 5000mm = 5m最大范围
            
            targets.forEach((target, index) => {
                const x = centerX + target.x * scale;
                const y = centerY - target.y * scale; // Y轴翻转
                
                // 绘制目标点
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, 2 * Math.PI);
                ctx.fillStyle = `hsl(${index * 120}, 70%, 60%)`;
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 绘制目标轨迹
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.strokeStyle = `hsla(${index * 120}, 70%, 60%, 0.5)`;
                ctx.lineWidth = 1;
                ctx.stroke();
                
                // 绘制目标信息
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`T${index + 1}`, x + 10, y - 10);
                ctx.fillText(`${target.distance.toFixed(1)}m`, x + 10, y + 5);
            });
        }
        
        // 更新目标列表
        function updateTargetList() {
            const targetListEl = document.getElementById('targetList');
            
            if (targets.length === 0) {
                targetListEl.innerHTML = '<div style="text-align: center; color: #888; padding: 20px;">暂无检测到目标</div>';
                return;
            }
            
            let html = '';
            targets.forEach((target, index) => {
                html += `
                    <div class="target-item">
                        <div class="target-coords">目标 ${index + 1}: (${target.x.toFixed(0)}, ${target.y.toFixed(0)})</div>
                        <div class="target-details">
                            距离: ${target.distance.toFixed(2)}m | 角度: ${target.angle.toFixed(1)}°
                        </div>
                    </div>
                `;
            });
            targetListEl.innerHTML = html;
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('detectionCount').textContent = detectionCount;
            document.getElementById('maxDistance').textContent = maxDistance.toFixed(2);
            document.getElementById('activeTargets').textContent = targets.length;
        }
        
        // 添加日志
        function addLog(message) {
            const logEl = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div><span class="timestamp">[${timestamp}]</span> ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        // 获取雷达数据
        async function fetchRadarData() {
            try {
                const response = await fetch('/api/targets');
                const data = await response.json();
                
                if (data.success) {
                    targets = data.targets;
                    detectionCount++;
                    
                    // 更新最大距离
                    targets.forEach(target => {
                        if (target.distance > maxDistance) {
                            maxDistance = target.distance;
                        }
                    });
                    
                    drawRadarGrid();
                    drawTargets();
                    updateTargetList();
                    updateStats();
                    
                    // 更新连接状态
                    document.getElementById('statusDot').className = 'status-dot status-online';
                    document.getElementById('statusText').textContent = '连接状态: 在线';
                    
                    if (targets.length > 0) {
                        addLog(`检测到 ${targets.length} 个目标`);
                    }
                } else {
                    throw new Error(data.message || '获取数据失败');
                }
            } catch (error) {
                console.error('Error fetching radar data:', error);
                document.getElementById('statusDot').className = 'status-dot status-offline';
                document.getElementById('statusText').textContent = '连接状态: 离线';
                addLog(`错误: ${error.message}`);
            }
        }
          // 控制函数
        function startDetection() {
            // 向后端发送开始检测请求
            fetch('/api/detection/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        isDetecting = true;
                        addLog('开始目标检测');
                        
                        // 定时获取数据
                        if (!dataInterval) {
                            dataInterval = setInterval(() => {
                                if (isDetecting) {
                                    fetchRadarData();
                                }
                            }, 500); // 每500ms更新一次
                        }
                    } else {
                        addLog('启动检测失败');
                    }
                })
                .catch(error => {
                    addLog(`启动检测错误: ${error.message}`);
                });
        }
        
        function stopDetection() {
            // 向后端发送停止检测请求
            fetch('/api/detection/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        isDetecting = false;
                        addLog('停止目标检测');
                        
                        // 清停定时器
                        if (dataInterval) {
                            clearInterval(dataInterval);
                            dataInterval = null;
                        }
                    } else {
                        addLog('停止检测失败');
                    }
                })
                .catch(error => {
                    addLog(`停止检测错误: ${error.message}`);
                });
        }
        
        function clearHistory() {
            fetch('/api/clear', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('历史数据已清除');
                    }
                });
        }
        
        function downloadData() {
            fetch('/api/download')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'radar_data.json';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    addLog('数据导出完成');
                });        }
        
        // 检查后端检测状态
        function checkDetectionStatus() {
            fetch('/api/detection/status')
                .then(response => response.json())
                .then(data => {
                    isDetecting = data.isDetecting;
                    if (isDetecting) {
                        addLog('检测已启用');
                        // 如果后端检测已启用，开始获取数据
                        if (!dataInterval) {
                            dataInterval = setInterval(() => {
                                if (isDetecting) {
                                    fetchRadarData();
                                }
                            }, 500);
                        }
                    } else {
                        addLog('检测已停用');
                    }
                })
                .catch(error => {
                    addLog(`检查状态错误: ${error.message}`);
                });
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            addLog('系统初始化完成');
            checkDetectionStatus(); // 检查后端检测状态
            
            // 窗口大小改变时重新绘制
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    initCanvas();
                    drawTargets();
                }, 100);
            });
        });
    </script>
</body>
</html>
