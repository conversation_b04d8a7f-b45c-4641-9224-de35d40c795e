#ifndef SECRETS_H
#define SECRETS_H

// WiFi Station Mode (connect to existing WiFi)
const char* WIFI_SSID = "HOME";
const char* WIFI_PASSWORD = "nb9d30@24zd";

// WiFi Access Point Mode (create own WiFi network for LD2450 radar)
const char* RADAR_AP_SSID = "ESP32-LD2450";
const char* RADAR_AP_PASSWORD = "12345678";
const bool USE_AP_MODE = false; // Set to false to use station mode instead

// Web Server settings for radar visualization
const int WEB_SERVER_PORT = 80;
const char* MDNS_HOST_NAME = "esp32ld2450"; // Access via esp32ld2450.local


#endif // SECRETS_H
